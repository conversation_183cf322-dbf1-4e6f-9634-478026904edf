from django.db import models
from mas.models import MasUsagePurpose, MasPosition, MasUserType

class DownloadRequester(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    masUserType = models.ForeignKey(MasUserType, on_delete=models.PROTECT, db_column='MasUserTypeId')
    departmentName = models.CharField(db_column='DepartmentName', max_length=500, db_collation='Thai_CI_AI')
    coordinatorName = models.CharField(db_column='CoordinatorName', max_length=500, db_collation='Thai_CI_AI')
    masPosition = models.ForeignKey(MasPosition, on_delete=models.PROTECT, db_column='MasPositionId')
    positionOther = models.CharField(db_column='PositionOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    masUsagePurpose = models.ForeignKey(MasUsagePurpose, on_delete=models.PROTECT, db_column='MasUsagePurposeId')
    usagePurposeOther = models.CharField(db_column='UsagePurposeOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    menu = models.CharField(db_column='Menu', max_length=200, db_collation='Thai_CI_AI')
    ipAddress = models.CharField(db_column='IpAddress', max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    browser = models.CharField(db_column='Browser', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    version = models.CharField(db_column='Version', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    platform = models.CharField(db_column='Platform', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    createDate = models.DateTimeField(db_column='CreateDate',auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'DownloadRequester'


class DownloadRequesterDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    downloadRequester = models.ForeignKey(DownloadRequester, on_delete=models.PROTECT, db_column='DownloadRequesterId')
    type = models.CharField(db_column='Type', max_length=200, db_collation='Thai_CI_AI')
    refid = models.IntegerField(db_column='RefId')

    class Meta:
        managed = False
        db_table = 'DownloadRequesterDetail'



class DownloadChartData(models.Model):
    label = []
    data = []
    
    def __str__(self):
        return self