from rest_framework import serializers
from .models import ComponentType, ComponentGroup, Component, StandardComponent, TestingCenterComponent, ManufacturerComponent, Standard, TestingCenter, Manufacturer
from mas.serializers import StandardSerializer
from testings.serializers import TestingCenterSerializer
from manufacturer.serializers import ManufacturerSerializer
from RTRDA.serializers import BaseModelSerializer

class ComponentTypeSerializer(BaseModelSerializer):
    class Meta:
        model = ComponentType
        fields = '__all__'

class ComponentGroupSerializer(BaseModelSerializer):
    componentType = ComponentTypeSerializer(read_only=True)
    componentTypeId = serializers.PrimaryKeyRelatedField(
        source='componentType', 
        queryset=ComponentType.objects.all()
    )
    class Meta:
        model = ComponentGroup
        fields = '__all__'

class ComponentSerializer(BaseModelSerializer):
    componentType = ComponentTypeSerializer(read_only=True)
    componentTypeId = serializers.PrimaryKeyRelatedField(
        source='componentType', 
        queryset=ComponentType.objects.all()
    )
    componentGroup = ComponentGroupSerializer(read_only=True)
    componentGroupId = serializers.PrimaryKeyRelatedField(
        source='componentGroup', 
        queryset=ComponentGroup.objects.all()
    )
    
    class Meta:
        model = Component
        fields = '__all__'

# Simplified Component serializer for use in TestingCenterComponentSerializer
class SimpleComponentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Component
        fields = ['id', 'componentType', 'componentGroup', 'majorSubSystem', 'majorComponents', 'subComponents', 'status']

class StandardComponentSerializer(serializers.ModelSerializer):
    standard = StandardSerializer(read_only=True)
    component = ComponentSerializer(read_only=True)
    standardId = serializers.PrimaryKeyRelatedField(
        source='standard', 
        queryset=Standard.objects.all()
    )
    componentId = serializers.PrimaryKeyRelatedField(
        source='component', 
        queryset=Component.objects.all()
    )
    
    class Meta:
        model = StandardComponent
        fields = '__all__'

class TestingCenterComponentSerializer(serializers.ModelSerializer):
    testingCenter = TestingCenterSerializer(read_only=True)
    component = SimpleComponentSerializer(read_only=True)
    testingCenterId = serializers.PrimaryKeyRelatedField(
        source='testingCenter',
        queryset=TestingCenter.objects.all()
    )
    componentId = serializers.PrimaryKeyRelatedField(
        source='component', 
        queryset=Component.objects.all()
    )
    
    class Meta:
        model = TestingCenterComponent
        fields = '__all__'

class ManufacturerComponentSerializer(serializers.ModelSerializer):
    manufacturer = ManufacturerSerializer(read_only=True)
    component = ComponentSerializer(read_only=True)
    manufacturerId = serializers.PrimaryKeyRelatedField(
        source='manufacturer',
        queryset=Manufacturer.objects.all()
    )
    componentId = serializers.PrimaryKeyRelatedField(
        source='component', 
        queryset=Component.objects.all()
    )
    
    class Meta:
        model = ManufacturerComponent
        fields = '__all__' 