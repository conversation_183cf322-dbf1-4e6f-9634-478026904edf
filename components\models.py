from django.db import models
from RTRDA.model import BaseModel
from mas.models import Standard
from testings.models import TestingCenter
from manufacturer.models import Manufacturer


class ComponentTypeMain(BaseModel):
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')
    image = models.CharField(db_column='Image', max_length=100, db_collation='Thai_CI_AI')
    iconfile = models.CharField(db_column='IconFile', max_length=100, db_collation='Thai_CI_AI')
    views = models.IntegerField(db_column='Views')
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'ComponentTypeMain'


class ComponentTypeSub(BaseModel):
    componentTypeMain = models.ForeignKey(ComponentTypeMain, on_delete=models.PROTECT, db_column='ComponentTypeMainId')
    name = models.<PERSON><PERSON><PERSON><PERSON>(db_column='Name', max_length=100, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status')
    views = models.IntegerField(db_column='Views')

    class Meta:
        managed = False
        db_table = 'ComponentTypeSub'



class ComponentType(BaseModel):
    componentTypeMain = models.ForeignKey(ComponentTypeMain, on_delete=models.PROTECT, db_column='ComponentTypeMainId')
    componentTypeSub = models.ForeignKey(ComponentTypeSub, on_delete=models.PROTECT, db_column='ComponentTypeSubId')
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'ComponentType'


class ComponentGroup(BaseModel):
    componentTypeMain = models.ForeignKey(ComponentTypeMain, on_delete=models.PROTECT, db_column='ComponentTypeMainId')
    componentTypeSub = models.ForeignKey(ComponentTypeSub, on_delete=models.PROTECT, db_column='ComponentTypeSubId')
    componentType = models.ForeignKey(ComponentType, on_delete=models.PROTECT, db_column='ComponentTypeId')
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'ComponentGroup'


class Component(BaseModel):
    componentTypeMain = models.ForeignKey(ComponentTypeMain, on_delete=models.PROTECT, db_column='ComponentTypeMainId')
    componentTypeSub = models.ForeignKey(ComponentTypeSub, on_delete=models.PROTECT, db_column='ComponentTypeSubId')
    componentType = models.ForeignKey(ComponentType, on_delete=models.PROTECT, db_column='ComponentTypeId')
    componentGroup = models.ForeignKey(ComponentGroup, on_delete=models.PROTECT, db_column='ComponentGroupId')
    majorSubSystem = models.CharField(db_column='MajorSubSystem', max_length=500, db_collation='Thai_CI_AI')
    majorComponents = models.CharField(db_column='MajorComponents', max_length=500, db_collation='Thai_CI_AI')
    subComponents = models.CharField(db_column='SubComponents', max_length=500, db_collation='Thai_CI_AI')
    views = models.IntegerField(db_column='Views')
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'Component'


class StandardComponent(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    standard = models.ForeignKey(Standard, on_delete=models.PROTECT, db_column='StandardId')
    component = models.ForeignKey(Component, on_delete=models.PROTECT, db_column='ComponentId')

    class Meta:
        managed = False
        db_table = 'StandardComponent'


class TestingCenterComponent(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    testingCenter = models.ForeignKey(TestingCenter, on_delete=models.PROTECT, db_column='TestingCenterId')
    component = models.ForeignKey(Component, on_delete=models.PROTECT, db_column='ComponentId')

    class Meta:
        managed = False
        db_table = 'TestingCenterComponent'


class ManufacturerComponent(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=1, db_collation='Thai_CI_AI')
    manufacturer = models.ForeignKey(Manufacturer, on_delete=models.PROTECT, db_column='ManufacturerId')
    component = models.ForeignKey(Component, on_delete=models.PROTECT, db_column='ComponentId')

    class Meta:
        managed = False
        db_table = 'ManufacturerComponent'
