from django.db import models
from RTRDA.model import BaseModel


class TrainingCategory(BaseModel):
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status', default=True)

    class Meta:
        managed = False
        db_table = 'TrainingCategory'

class Training(BaseModel):
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')
    detail = models.TextField(db_column='Detail', db_collation='Thai_CI_AI')
    startDate = models.DateTimeField(db_column='StartDate')
    endDate = models.DateTimeField(db_column='EndDate')
    trainingCategory = models.ForeignKey(
        TrainingCategory,
        on_delete=models.PROTECT,
        db_column='TrainingCategoryId')
    cover = models.ImageField(db_column='Cover', upload_to='trainings/', blank=True, null=True)
    status = models.BooleanField(db_column='Status', default=True)
    views = models.IntegerField(db_column='Views', default=0)

    class Meta:
        managed = False
        db_table = 'Training'